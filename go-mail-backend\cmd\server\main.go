package main

import (
	"context"
	"flag"
	"fmt"
	"go-mail/internal/api"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/types"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"
)

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string
	Port         int
	DatabasePath string
	AccountsFile string
	JWTSecret    string
	AESKey       string
	Debug        bool
}

// DefaultConfig 默认配置
func DefaultConfig() *ServerConfig {
	return &ServerConfig{
		Host:         "0.0.0.0",
		Port:         8080,
		DatabasePath: "./data/go-mail.db",
		AccountsFile: "./data/accounts.json",
		JWTSecret:    "go-mail-jwt-secret-key",
		AESKey:       "go-mail-aes-key-32-bytes-long!!",
		Debug:        false,
	}
}

func main() {
	// 解析命令行参数
	var (
		host         = flag.String("host", "0.0.0.0", "服务器监听地址")
		port         = flag.Int("port", 8080, "服务器监听端口")
		databasePath = flag.String("db", "./data/go-mail.db", "数据库文件路径")
		accountsFile = flag.String("accounts", "./data/accounts.json", "账户文件路径")
		debug        = flag.Bool("debug", false, "启用调试模式")
		version      = flag.Bool("version", false, "显示版本信息")
	)
	flag.Parse()

	// 显示版本信息
	if *version {
		printBanner()
		fmt.Println("Go-Mail 临时邮箱服务平台")
		fmt.Println("版本: 1.0.0")
		fmt.Println("构建时间:", time.Now().Format("2006-01-02 15:04:05"))
		fmt.Println("Go版本:", "1.23+")
		fmt.Println("作者: 86")
		return
	}

	// 创建配置
	config := DefaultConfig()
	if *host != "" {
		config.Host = *host
	}
	if *port != 0 {
		config.Port = *port
	}
	if *databasePath != "" {
		config.DatabasePath = *databasePath
	}
	if *accountsFile != "" {
		config.AccountsFile = *accountsFile
	}
	config.Debug = *debug

	// 设置日志
	logger := setupLogger(config.Debug)
	slog.SetDefault(logger)

	logger.Info("启动 Go-Mail 临时邮箱服务平台",
		"version", "1.0.0",
		"host", config.Host,
		"port", config.Port,
		"debug", config.Debug)

	// 创建数据目录
	if err := createDataDirectories(config); err != nil {
		logger.Error("创建数据目录失败", "error", err)
		os.Exit(1)
	}

	// 初始化数据库
	logger.Info("初始化数据库", "path", config.DatabasePath)
	db, err := database.NewDatabase(config.DatabasePath)
	if err != nil {
		logger.Error("数据库初始化失败", "error", err)
		os.Exit(1)
	}
	defer db.Close()

	// 初始化邮件管理器
	logger.Info("初始化邮件管理器", "accounts_file", config.AccountsFile)
	mailManager, err := initializeMailManager(config.AccountsFile, db)
	if err != nil {
		logger.Error("邮件管理器初始化失败", "error", err)
		os.Exit(1)
	}

	// 启动邮件管理器
	logger.Info("启动邮件管理器")
	ctx := context.Background()
	if err := mailManager.Start(ctx); err != nil {
		logger.Error("邮件管理器启动失败", "error", err)
		os.Exit(1)
	}

	// 创建API服务器配置
	serverConfig := &api.ServerConfig{
		Host:         config.Host,
		Port:         config.Port,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		JWTSecret:    config.JWTSecret,
		AESKey:       config.AESKey,
		Debug:        config.Debug,
	}

	// 创建HTTP服务器
	logger.Info("创建HTTP服务器")
	server := api.NewServer(mailManager, db, serverConfig)

	// 创建上下文用于优雅关闭
	serverCtx, serverCancel := context.WithCancel(context.Background())
	defer serverCancel()

	// 启动服务器
	go func() {
		logger.Info("启动HTTP服务器",
			"address", fmt.Sprintf("%s:%d", config.Host, config.Port))

		if err := server.StartWithContext(serverCtx); err != nil {
			logger.Error("HTTP服务器启动失败", "error", err)
			serverCancel()
		}
	}()

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case sig := <-sigChan:
		logger.Info("收到信号，开始关闭服务器", "signal", sig)
	case <-serverCtx.Done():
		logger.Info("服务器上下文已取消")
	}

	// 优雅关闭
	logger.Info("正在关闭服务器...")
	serverCancel()

	// 停止邮件管理器
	logger.Info("正在停止邮件管理器...")
	if err := mailManager.Stop(ctx); err != nil {
		logger.Warn("停止邮件管理器失败", "error", err)
	} else {
		logger.Info("邮件管理器已停止")
	}

	// 等待一段时间让服务器完成关闭
	time.Sleep(2 * time.Second)
	logger.Info("服务器已关闭")
}

// setupLogger 设置日志记录器
func setupLogger(debug bool) *slog.Logger {
	var level slog.Level
	if debug {
		level = slog.LevelDebug
	} else {
		level = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level: level,
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// 格式化时间
			if a.Key == slog.TimeKey {
				a.Value = slog.StringValue(time.Now().Format("2006-01-02 15:04:05"))
			}
			return a
		},
	}

	handler := slog.NewTextHandler(os.Stdout, opts)
	return slog.New(handler)
}

// createDataDirectories 创建数据目录
func createDataDirectories(config *ServerConfig) error {
	// 创建数据库目录
	dbDir := filepath.Dir(config.DatabasePath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %w", err)
	}

	// 创建账户文件目录
	accountsDir := filepath.Dir(config.AccountsFile)
	if err := os.MkdirAll(accountsDir, 0755); err != nil {
		return fmt.Errorf("创建账户文件目录失败: %w", err)
	}

	// 创建静态文件目录
	webDir := "./web"
	if err := os.MkdirAll(webDir, 0755); err != nil {
		return fmt.Errorf("创建静态文件目录失败: %w", err)
	}

	return nil
}

// initializeMailManager 初始化邮件管理器
func initializeMailManager(accountsFile string, db *database.Database) (*manager.MailManager, error) {
	// 检查账户文件是否存在
	if _, err := os.Stat(accountsFile); os.IsNotExist(err) {
		// 创建示例账户文件
		if err := createSampleAccountsFile(accountsFile); err != nil {
			return nil, fmt.Errorf("创建示例账户文件失败: %w", err)
		}
	}

	// 创建邮件管理器配置
	config := types.DefaultManagerConfig()

	// 创建邮件管理器并设置数据库连接
	mailManager := manager.NewMailManagerWithDatabase(config, db)

	// TODO: 这里可以添加账户加载逻辑
	// 目前使用默认配置，账户可以通过API动态添加

	return mailManager, nil
}

// createSampleAccountsFile 创建示例账户文件
func createSampleAccountsFile(accountsFile string) error {
	sampleAccounts := `{
  "accounts": [
    {
      "username": "<EMAIL>",
      "password": "your_password_here",
      "status": "inactive",
      "note": "请替换为真实的Mail.com账户信息"
    }
  ]
}`

	return os.WriteFile(accountsFile, []byte(sampleAccounts), 0644)
}

// printBanner 打印启动横幅
func printBanner() {
	banner := `
  ____         __  __       _ _ 
 / ___| ___   |  \/  | __ _(_) |
| |  _ / _ \  | |\/| |/ _` + "`" + ` | | |
| |_| | (_) | | |  | | (_| | | |
 \____|\___/  |_|  |_|\__,_|_|_|
                                
临时邮箱服务平台 v1.0.0
`
	fmt.Print(banner)
}

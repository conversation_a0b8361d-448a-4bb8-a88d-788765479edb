package api

import (
	"context"
	"fmt"
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/scheduler"
	"go-mail/internal/services"
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Server HTTP服务器结构
type Server struct {
	engine      *gin.Engine
	mailManager *manager.MailManager
	database    *database.Database
	services    *services.Services
	auth        *auth.Auth
	scheduler   *scheduler.Scheduler
	logger      *slog.Logger
	config      *ServerConfig
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	JWTSecret    string        `json:"jwt_secret"`
	AESKey       string        `json:"aes_key"`
	Debug        bool          `json:"debug"`
}

// DefaultServerConfig 默认服务器配置
func DefaultServerConfig() *ServerConfig {
	return &ServerConfig{
		Host:         "0.0.0.0",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		JWTSecret:    "go-mail-jwt-secret-key",
		AESKey:       "go-mail-aes-key-32-bytes-long!!",
		Debug:        false,
	}
}

// NewServer 创建新的HTTP服务器
func NewServer(mailManager *manager.MailManager, database *database.Database, config *ServerConfig) *Server {
	if config == nil {
		config = DefaultServerConfig()
	}

	// 设置Gin模式
	if !config.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建认证服务
	authService := auth.NewAuth(config.JWTSecret, config.AESKey)

	// 创建业务服务
	servicesInstance := services.NewServices(mailManager, database, authService)

	// 创建任务调度器
	taskScheduler := scheduler.NewScheduler(slog.Default())

	// 添加定时任务
	taskScheduler.AddTask(scheduler.NewMailboxCleanupTask(servicesInstance.Mailbox, slog.Default()))
	taskScheduler.AddTask(scheduler.NewAccountHealthCheckTask(mailManager, database, slog.Default()))
	taskScheduler.AddTask(scheduler.NewSystemCleanupTask(database, slog.Default()))
	taskScheduler.AddTask(scheduler.NewStatisticsUpdateTask(database, slog.Default()))
	taskScheduler.AddTask(scheduler.NewSessionCleanupTask(mailManager, slog.Default()))

	// 添加批量操作清理任务（防止卡住的批量操作）
	taskScheduler.AddTask(scheduler.NewBatchOperationCleanupTask(database, slog.Default()))

	// 添加邮箱管理相关任务
	taskScheduler.AddTask(scheduler.NewMailboxVerificationTask(mailManager, database, servicesInstance.MailboxManagement, slog.Default()))
	taskScheduler.AddTask(scheduler.NewMailboxHealthMonitorTask(mailManager, database, slog.Default()))

	// 创建Gin引擎
	engine := gin.New()

	server := &Server{
		engine:      engine,
		mailManager: mailManager,
		database:    database,
		services:    servicesInstance,
		auth:        authService,
		scheduler:   taskScheduler,
		logger:      slog.Default(),
		config:      config,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 全局中间件
	s.engine.Use(middleware.PanicRecovery())        // Panic恢复中间件（必须在最前面）
	s.engine.Use(middleware.RequestID())            // 请求ID生成
	s.engine.Use(middleware.EnhancedErrorHandler()) // 增强错误处理和日志
	s.engine.Use(middleware.CORS())                 // 跨域处理
	// 注意：移除了默认的 gin.Logger() 和 gin.Recovery()，使用我们自定义的增强版本

	// API v1 路由组
	v1 := s.engine.Group("/api/v1")

	// 管理后台认证路由
	authGroup := v1.Group("/auth")
	{
		authHandler := handlers.NewAuthHandler(s.services.Auth, s.database)
		s.logger.Info("注册认证路由", "prefix", "/api/v1/auth")
		authGroup.POST("/login", authHandler.Login)
		authGroup.POST("/refresh", authHandler.RefreshToken)
		authGroup.POST("/logout", middleware.JWTAuth(s.auth), authHandler.Logout)
		authGroup.GET("/validate", middleware.JWTAuth(s.auth), authHandler.ValidateToken)
		authGroup.GET("/me", middleware.JWTAuth(s.auth), authHandler.GetProfile)
		s.logger.Info("认证路由注册完成", "routes", []string{
			"POST /api/v1/auth/login",
			"POST /api/v1/auth/refresh",
			"POST /api/v1/auth/logout",
			"GET /api/v1/auth/validate",
			"GET /api/v1/auth/me",
		})
	}

	// 激活码管理路由（管理后台）
	activationGroup := v1.Group("/activation")
	activationGroup.Use(middleware.JWTAuth(s.auth))
	{
		activationHandler := handlers.NewActivationHandler(s.services.Activation)
		activationGroup.POST("/generate", activationHandler.GenerateCodes)
		activationGroup.POST("/verify", activationHandler.VerifyCode)
		activationGroup.GET("/list", activationHandler.ListCodes)
		activationGroup.GET("/:id", activationHandler.GetCodeInfo)
		activationGroup.DELETE("/:id", activationHandler.DeleteCode)
		activationGroup.POST("/batch-delete", activationHandler.BatchDeleteCodes)
		activationGroup.PUT("/:id/expire", activationHandler.ExpireCode)
		activationGroup.GET("/export", activationHandler.ExportCodes)
		activationGroup.GET("/stats", activationHandler.GetStats)
	}

	// 客户端路由组
	clientGroup := v1.Group("/client")
	clientGroup.Use(middleware.ClientAuth(s.auth, s.database))
	{
		// 客户端激活码验证
		clientActivationGroup := clientGroup.Group("/activation")
		{
			clientActivationHandler := handlers.NewClientActivationHandler(s.services.Activation)
			clientActivationGroup.POST("/verify", clientActivationHandler.VerifyActivationCode)
		}

		// 客户端邮箱管理
		clientMailboxGroup := clientGroup.Group("/mailbox")
		{
			clientMailboxHandler := handlers.NewClientMailboxHandler(s.services.Mailbox)
			clientMailboxGroup.POST("/allocate", clientMailboxHandler.AllocateMailbox)
			clientMailboxGroup.POST("/query", clientMailboxHandler.QueryMails)
			clientMailboxGroup.POST("/mail/detail", clientMailboxHandler.GetMailDetail)
			clientMailboxGroup.POST("/release", clientMailboxHandler.ReleaseMailbox)
			clientMailboxGroup.POST("/status", clientMailboxHandler.GetMailboxStatus)
		}
	}

	// 系统监控路由（管理后台）
	monitorGroup := v1.Group("/monitor")
	monitorGroup.Use(middleware.JWTAuth(s.auth))
	{
		monitorHandler := handlers.NewMonitorHandler(s.services.Monitor, s.scheduler)
		monitorGroup.GET("/statistics", monitorHandler.GetStatistics)
		monitorGroup.GET("/accounts", monitorHandler.GetAccountStatus)
		monitorGroup.GET("/sessions", monitorHandler.GetSessionStatus)
		monitorGroup.GET("/logs", monitorHandler.GetLogs)
		monitorGroup.GET("/tasks", monitorHandler.GetTaskStatus)
		monitorGroup.POST("/tasks/:task/:action", monitorHandler.ControlTask)
	}

	// 系统配置路由（管理后台）
	configGroup := v1.Group("/config")
	configGroup.Use(middleware.JWTAuth(s.auth))
	{
		configHandler := handlers.NewConfigHandler(s.services.Config)
		configGroup.GET("", configHandler.GetConfig)
		configGroup.PUT("", configHandler.UpdateConfig)
	}

	// 系统管理路由（管理后台）
	systemGroup := v1.Group("/system")
	systemGroup.Use(middleware.JWTAuth(s.auth))
	{
		systemHandler := handlers.NewSystemHandler(s.database, s.scheduler)
		systemGroup.POST("/cleanup", systemHandler.CleanupSystem)
		systemGroup.POST("/backup", systemHandler.BackupSystem)
		systemGroup.GET("/version", systemHandler.GetVersionInfo)
	}

	// 邮箱管理路由（管理后台）
	mailboxGroup := v1.Group("/mailbox")
	mailboxGroup.Use(middleware.JWTAuth(s.auth))
	mailboxGroup.Use(middleware.SecurityHeaders())
	mailboxGroup.Use(middleware.OperationAudit())
	{
		mailboxHandler := handlers.NewMailboxManagementHandler(s.services.MailboxManagement)
		s.logger.Info("注册邮箱管理路由", "prefix", "/api/v1/mailbox")

		// 创建频率限制器
		rateLimiter := middleware.NewRateLimiter()

		// 批量操作（应用严格的频率和并发限制）
		mailboxGroup.POST("/batch-import",
			rateLimiter.ImportRateLimit(),
			middleware.BatchImportSizeLimit(),
			middleware.BatchOperationConcurrencyLimit(),
			mailboxHandler.BatchImportAccounts)

		mailboxGroup.POST("/verify",
			rateLimiter.VerificationRateLimit(),
			middleware.VerificationConcurrencyLimit(),
			mailboxHandler.StartVerificationTask)

		mailboxGroup.GET("/batch-operation/:operation_id", mailboxHandler.GetBatchOperationStatus)

		// 账户管理
		mailboxGroup.GET("/accounts", mailboxHandler.GetAccountsList)
		mailboxGroup.DELETE("/accounts/:id", mailboxHandler.DeleteAccount)
		mailboxGroup.PUT("/accounts/:id/status", mailboxHandler.ToggleAccountStatus)
		mailboxGroup.POST("/batch-delete", mailboxHandler.BatchDeleteAccounts)
		mailboxGroup.POST("/batch-disable", mailboxHandler.BatchDisableAccounts)

		// 任务控制（应用频率限制）
		mailboxGroup.POST("/task-control",
			rateLimiter.BatchOperationRateLimit(),
			mailboxHandler.ControlTask)

		mailboxGroup.GET("/scheduler-status", mailboxHandler.GetTaskSchedulerStatus)

		// 统计信息
		mailboxGroup.GET("/statistics", mailboxHandler.GetMailboxStatistics)

		// 任务日志管理
		taskLogHandler := handlers.NewTaskLogHandler(s.services.TaskLog)
		mailboxGroup.GET("/task-logs", taskLogHandler.GetTaskLogs)
		mailboxGroup.GET("/task-logs/:id/details", taskLogHandler.GetTaskLogDetail)
		mailboxGroup.POST("/task-logs", taskLogHandler.CreateTaskLog)
		mailboxGroup.PUT("/task-logs/:task_id", taskLogHandler.UpdateTaskLog)

		s.logger.Info("邮箱管理路由注册完成", "routes", []string{
			"POST /api/v1/mailbox/batch-import",
			"POST /api/v1/mailbox/verify",
			"GET /api/v1/mailbox/batch-operation/:operation_id",
			"GET /api/v1/mailbox/accounts",
			"DELETE /api/v1/mailbox/accounts/:id",
			"PUT /api/v1/mailbox/accounts/:id/status",
			"POST /api/v1/mailbox/batch-delete",
			"POST /api/v1/mailbox/batch-disable",
			"POST /api/v1/mailbox/task-control",
			"GET /api/v1/mailbox/scheduler-status",
			"GET /api/v1/mailbox/statistics",
			"GET /api/v1/mailbox/task-logs",
			"GET /api/v1/mailbox/task-logs/:id/details",
			"POST /api/v1/mailbox/task-logs",
			"PUT /api/v1/mailbox/task-logs/:task_id",
		})
	}

	// 健康检查路由
	s.engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Format(time.RFC3339),
			"version":   "1.0.0",
		})
	})

	// 静态文件服务（管理后台前端）
	s.engine.Static("/static", "./web/static")
	s.engine.StaticFile("/", "./web/index.html")
	s.engine.StaticFile("/favicon.ico", "./web/favicon.ico")
}

// Start 启动HTTP服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	s.logger.Info("启动HTTP服务器",
		"host", s.config.Host,
		"port", s.config.Port,
		"debug", s.config.Debug)

	server := &http.Server{
		Addr:         addr,
		Handler:      s.engine,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
	}

	return server.ListenAndServe()
}

// StartWithContext 带上下文启动HTTP服务器
func (s *Server) StartWithContext(ctx context.Context) error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	server := &http.Server{
		Addr:         addr,
		Handler:      s.engine,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
	}

	// 启动任务调度器
	if err := s.scheduler.Start(ctx); err != nil {
		s.logger.Error("任务调度器启动失败", "error", err)
		return err
	}

	// 启动服务器
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP服务器启动失败", "error", err)
		}
	}()

	s.logger.Info("HTTP服务器已启动",
		"host", s.config.Host,
		"port", s.config.Port)

	// 等待上下文取消
	<-ctx.Done()

	// 优雅关闭
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	s.logger.Info("正在关闭HTTP服务器...")

	// 停止任务调度器
	s.scheduler.Stop()
	s.logger.Info("任务调度器已停止")

	if err := server.Shutdown(shutdownCtx); err != nil {
		s.logger.Error("HTTP服务器关闭失败", "error", err)
		return err
	}

	s.logger.Info("HTTP服务器已关闭")
	return nil
}

// GetEngine 获取Gin引擎（用于测试）
func (s *Server) GetEngine() *gin.Engine {
	return s.engine
}

# 开发规范和规则


**项目结构参考：**
- `go-mail-client/` - 客户端代码
- `go-mail-backend/` - 后台管理系统
- `mail-frontend/` - 后台前端系统
**参考文件：**
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend/cmd/server/main.go` - 主函数，包含服务器启动、路由注册、中间件配置等
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend/internal/services/mailbox_management_helpers.go` - 邮箱验证任务管理，包含验证任务的创建、执行、日志记录、错误处理等全过程
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend/internal/database/database.go` - 数据库操作，包含数据库连接、事务处理、查询等
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend/internal/mail/client/login_client.go` - 登录Mail客户完整过程
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend/internal/mail/client/mail_client.go` - 邮件客户端，包含邮件列表、邮件内容、别名操作等完整过程
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend\cmd\single-login/` - 单账号测试流程（包含登录、取邮件列表、取邮件内容、别名操作等完整过程）
**API路由管理**
controller/
├── server.go                    # 主服务器和路由注册入口
├── admin/                       # 后台管理系统
│   ├── auth_api.go             # 管理员认证接口
│   ├── activation_api.go       # 激活码管理接口  
│   ├── mailbox_api.go          # 邮箱账户管理接口
│   ├── monitor_api.go          # 系统监控接口
│   ├── config_api.go           # 系统配置接口
│   └── system_api.go           # 系统管理接口
├── client/                      # 客户端接口
│   ├── auth_api.go             # 客户端认证接口
│   └── mailbox_api.go          # 客户端邮箱接口
└── common/                      # 通用接口
    ├── health_api.go           # 健康检查
    └── task_log_api.go         # 任务日志（可能被多个模块使用）

---


- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，不要运行（用户自己运行）

- API路由重构规则：按功能模块拆分API控制器到不同子目录（admin/、client/、common/），文件命名格式为{模块名}_api.go，保持路由注册功能完整，遵循Go项目最佳实践
